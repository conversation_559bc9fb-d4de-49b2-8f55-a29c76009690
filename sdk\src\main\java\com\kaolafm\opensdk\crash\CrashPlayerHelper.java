package com.kaolafm.opensdk.crash;

import android.content.Context;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.opensdk.OpenSDK;
import com.kaolafm.opensdk.api.CrashMessageBaseBean;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

import static android.media.AudioManager.AUDIOFOCUS_GAIN;
import static android.media.AudioManager.AUDIOFOCUS_GAIN_TRANSIENT;


/**
 * 插播播放器
 * 蔡佳彬
 */
public class CrashPlayerHelper implements AudioManager.OnAudioFocusChangeListener {
    private final String TAG = "CrashPlayerHelper";
    private static CrashPlayerHelper crashPlayerHelper;
    private MediaPlayer mediaPlayer;//播放器对象
    private AudioManager audioManager;

    private volatile List<CrashMessageBaseBean> immediatelyplayList = new ArrayList<>();//立即播放
    private volatile List<CrashMessageBaseBean> playList = new ArrayList<>();//延时播放
    private CrashMessageBaseBean crashMessageBaseBean;//当前播放的资源

    private int playIndex = 0;//播放的索引
    private volatile List<Icrashstate> icrashstate = new CopyOnWriteArrayList<>();//播放状态回调
    private IcrashPlay icrashPlay;//连续播放状态回调

    private boolean isPlayImmediatel = false;//是否在播放立即插播
    public boolean isPlayCrash = false;//是否正在播放插播
    public boolean isPlayEnd = false;//是否正常播放完成

    private String mUrl;
    private String needTermUrl;
    private boolean formUser = false;

    private Handler mainHandler;

    public static CrashPlayerHelper getInstance() {
        if (crashPlayerHelper == null) {
            synchronized (CrashPlayerHelper.class) {
                if (crashPlayerHelper == null) {
                    crashPlayerHelper = new CrashPlayerHelper();
                }
            }
        }
        return crashPlayerHelper;
    }

    public CrashPlayerHelper() {
        mediaPlayer = new MediaPlayer();//实例化播放器对象
        audioManager = (AudioManager) OpenSDK.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
        mainHandler = new Handler(Looper.getMainLooper());
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                initMediaPlayerListener();
            }
        });
    }

    /**
     * 添加立即播放
     *
     * @param messageBean
     */
    public CrashPlayerHelper addImmediatelyplayDate(CrashMessageBaseBean messageBean) {
        Set<CrashMessageBaseBean> set = new HashSet<>(immediatelyplayList);
        if (set.contains(messageBean)) {
            return this;
        }
        immediatelyplayList.add(messageBean);
        return this;
    }

    /**
     * 添加延时播放
     *
     * @param messageBean
     */
    public CrashPlayerHelper addPlayDate(CrashMessageBaseBean messageBean) {
        Set<CrashMessageBaseBean> set = new HashSet<>(playList);
        if (set.contains(messageBean)) {
            return this;
        }
        playList.add(messageBean);
        return this;
    }

    /**
     * 通过消息id删除指定消息
     *
     * @param msgId
     */
    public boolean getMessageByIdRemove(String msgId) {
        boolean b = false;//是否找到消息
        for (int i = 0; i < playList.size(); i++) {
            if (playList.get(i).getMsgId().equals(msgId)) {
                playList.remove(i);
                b = true;
                break;
            }
        }
        if (!b) {
            for (int i = 0; i < immediatelyplayList.size(); i++) {
                if (immediatelyplayList.get(i).getMsgId().equals(msgId)) {
                    immediatelyplayList.remove(i);
                    b = true;
                    break;
                }
            }
        }
        return b;
    }

    /**
     * 添加播放监听
     *
     * @param icrashstate
     */
    public CrashPlayerHelper setIcrashstate(Icrashstate icrashstate) {
        this.icrashstate.add(icrashstate);
        return this;
    }

    /**
     * 移除播放监听
     */
    public void removeIcrashstate() {
        if (this.icrashstate.size() > 0) {
            this.icrashstate.clear();
        }
    }

    /**
     * 添加看徐播放监听
     *
     * @param icrashPlay
     * @return
     */
    public CrashPlayerHelper setIcrashPlay(IcrashPlay icrashPlay) {
        this.icrashPlay = icrashPlay;
        return this;
    }


    /**
     * 获取播放器
     *
     * @return
     */
    public MediaPlayer getMediaPlayer() {
        return mediaPlayer;
    }

    /**
     * 获取音频焦点
     */
    public int requestAudioFocus() {
//        AUDIOFOCUS_GAIN //长时间获得焦点
//        AUDIOFOCUS_GAIN_TRANSIENT //短暂性获得焦点，用完应立即释放
//        AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK //短暂性获得焦点并降音，可混音播放
//        AUDIOFOCUS_GAIN_TRANSIENT_EXCLUSIVE //短暂性获得焦点，录音或者语音识别
        int audioFocus = 0;
        if (audioManager != null) {
            audioFocus = audioManager.requestAudioFocus(this, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
        }
        return audioFocus;
    }

    /**
     * 获取音频焦点
     * 可添加属性设置音频配置
     */
    public int requestAudioFocus(AudioFocusRequest audioFocusRequest) {
        if (audioFocusRequest == null) {
            throw new IllegalArgumentException("AudioFocusRequest cannot be null");
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return audioManager.requestAudioFocus(audioFocusRequest);
        } else {
            throw new UnsupportedOperationException("AudioFocusRequest is not supported below API level 26");
        }
    }

    /**
     * 释放音频焦点
     */
    public void abandonAudioFocus() {
        if (audioManager != null)
            audioManager.abandonAudioFocus(this);
    }

    /**
     * 添加播放器监听
     */
    private void initMediaPlayerListener() {
        //设置缓冲监听
        mediaPlayer.setOnBufferingUpdateListener(new MediaPlayer.OnBufferingUpdateListener() {
            @Override
            public void onBufferingUpdate(MediaPlayer mp, int percent) {
                Log.d(TAG, "缓冲进度:" + percent);

            }
        });
        mediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
            @Override
            public boolean onError(MediaPlayer mp, int what, int extra) {
                Log.d(TAG, "播放出错：" + what);
                //释放音频焦点
                playEnd(true);
                return true;
            }
        });
        mediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                Log.d(TAG, "播放完毕");
                isPlayEnd = true;

                // 增加延迟确保音频完全播放完毕，避免快速切换导致的音频截断
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    playEnd();
                }, 100); // 200ms延迟，确保音频完全输出
            }
        });
        mediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                //异步准备完成
                if (crashMessageBaseBean != null && crashMessageBaseBean.isTerminatePlayMsg()) {
                    Log.w(TAG, "-----中止消息播放-----" + crashMessageBaseBean.getMsgId());
                    crashMessageBaseBean.setTerminatePlayMsg(false);
                    return;
                }
                Log.e(TAG, "===needTermUrl="+needTermUrl+", mUrl="+mUrl);
                if (!TextUtils.isEmpty(needTermUrl)){
                    if (TextUtils.equals(mUrl, needTermUrl)) {
                        Log.e(TAG, "===needTermUrl reset 05");
                        needTermUrl = "";
                        return;
                    }
                    needTermUrl = "";
                    Log.e(TAG, "===needTermUrl reset 04");
                }
                int audioFocus = 0;
                // 播放消息盒子,不需要云听持有音频焦点
                if(isFormUser()){
                    audioFocus = requestAudioFocus();
                }else{
                    // 被动推送的应急广播, 需要云听持有音频焦点
                    if(ijkHasFocus()){    //ijk有焦点的时候，应急广播才抢焦点
                        PlayerLogUtil.log(TAG,"当前云听持有音频焦点");
                        audioFocus = requestAudioFocus();
                    }else{
                        PlayerLogUtil.log(TAG,"当前云听没有持有音频焦点无法播放");
                    }
                }

                //audioFocus = requestAudioFocus();
                if (audioFocus == 1) {
                    Log.d(TAG, "-----获取音频焦点成功-----");
                    if (icrashstate != null && !icrashstate.isEmpty()) {
                        for (Icrashstate value : icrashstate) {
                            value.onBufferingUpdate(getCrashMessageBaseBean());
                        }
                    }
                    isPlayCrash = true;
                    mediaPlayer.start();
                } else {
                    Log.d(TAG, "-----获取音频焦点失败-----");
                    if (icrashstate != null && !icrashstate.isEmpty()) {
                        for (Icrashstate value : icrashstate) {
                            value.onPlayerFailed(Icrashstate.PlayerFailedType.FOCUS_GET_FAILED);
                        }
                    }
                    isPlayCrash = false;
                    removeDate();
                }
            }
        });
    }

    private boolean ijkHasFocus(){
        int ijkFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
        return ijkFocus == AUDIOFOCUS_GAIN || ijkFocus == AUDIOFOCUS_GAIN_TRANSIENT;
    }

    public void playEnd() {
        playEnd(false);
    }

    public void playEnd(boolean handleError) {
        if (isPlayImmediatel) {
            synchronized (immediatelyplayList) {
                if (immediatelyplayList.size() > playIndex)
                    immediatelyplayList.remove(playIndex);
            }
            //判断是否还有立即插播
            if (immediatelyplayList.size() == 0) {

                //释放音频焦点
//                abandonAudioFocus();
                notificationPlayState();
            }
        } else {
            synchronized (playList) {
                if (playList.size() > playIndex)
                    playList.remove(playIndex);
            }
            //延时插播完毕，判断是否有立即插播
            if (immediatelyplayList.size() == 0) {

                //释放音频焦点
//                abandonAudioFocus();
                notificationPlayState();
            }
        }

        // 增加延迟确保音频完全播放完毕，避免快速切换导致的音频截断
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            stopPlay(handleError);
        }, 100); // 250ms延迟，确保音频完全输出
    }

    private void notificationPlayState() {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                if (icrashstate != null && icrashstate.size() > 0) {
                    for (Icrashstate s : icrashstate) {
                        s.onCrashstate(isPlayEnd ? 0 : 1);
                    }
                    removeIcrashstate();
                }
            }
        });
    }

    public synchronized CrashMessageBaseBean getPlayDate() {
        int index = -1;
        String url = "";
        //判断播放的插播类型
        if (immediatelyplayList != null && immediatelyplayList.size() > 0) {
            for (int i = 0; i < immediatelyplayList.size(); i++) {
                if (Integer.parseInt(immediatelyplayList.get(i).getMsgLevel()) > index) {
                    index = i;
                }
            }
            isPlayImmediatel = true;
            crashMessageBaseBean = immediatelyplayList.get(index);
//            url = immediatelyplayList.get(index).getEventDescriptionPath();
        } else if (playList != null && playList.size() > 0) {   //2,3级消息，先展示2级消息
            index = getMinMsgLevelIndex(playList);
            isPlayImmediatel = false;
            crashMessageBaseBean = playList.get(index);
//            url = playList.get(index).getEventDescriptionPath();
        }
        playIndex = index;
        return crashMessageBaseBean;
    }

    public synchronized CrashMessageBaseBean getPlayDataImmediate() {
        int index = -1;
        String url = "";
        //判断播放的插播类型
        if (immediatelyplayList != null && immediatelyplayList.size() > 0) {
            for (int i = 0; i < immediatelyplayList.size(); i++) {
                if (Integer.parseInt(immediatelyplayList.get(i).getMsgLevel()) > index) {
                    index = i;
                }
            }
            crashMessageBaseBean = immediatelyplayList.get(index);
        } else if (playList != null && playList.size() > 0) {   //2,3级消息，先展示2级消息
            index = getMinMsgLevelIndex(playList);
            crashMessageBaseBean = playList.get(index);
        }
        return crashMessageBaseBean;
    }

    /**最紧急的，且最先到达的那个消息*/
    private int getMinMsgLevelIndex(List<CrashMessageBaseBean> list){
        if(list.size() == 1){
            return 0;
        }

        int tmpIndex = list.size() - 1;
        try {
            for(int i = list.size() - 2; i >= 0; i--){
                int currLevel = Integer.parseInt(playList.get(i).getMsgLevel());
                int tmpLevel = Integer.parseInt(playList.get(tmpIndex).getMsgLevel());

                if ( currLevel <= tmpLevel ) {  //更早的消息在队列前部，同等级的也找最早到达的消息
                    tmpIndex = i;
                }
            }
        } catch (Exception e){
            Log.d("CrashPlayerHelper", "getMinMsgLevelIndex", e);
        }

        return tmpIndex;
    }

    /**
     * 当前播放的资源是否是立即插播
     *
     * @return
     */
    public boolean isPlayImmediatel() {
        return isPlayImmediatel;
    }

    /**
     * 获取当前正在播放的资源
     *
     * @return
     */
    public CrashMessageBaseBean getCrashMessageBaseBean() {
        getPlayDataImmediate();
        return crashMessageBaseBean;
    }

    /**
     * 移除当前正在播放的
     */
    private void removeDate() {
        if (isPlayImmediatel) {
            if (immediatelyplayList.size() > playIndex)
                immediatelyplayList.remove(playIndex);
        } else {
            if (playList.size() > playIndex)
                playList.remove(playIndex);
        }
    }

    /**
     * 移除立即插播主要针对于连续播放并且语音播放关闭的情况
     */
    public CrashPlayerHelper removeImmediatelDate() {
        getPlayDate();
        if (immediatelyplayList.size() > playIndex)
            immediatelyplayList.remove(playIndex);
        return this;
    }

    /**
     * 开始播放
     */
    public synchronized void startPlay() {
        startPlay(false);  // 默认非用户主动
    }

    /**
     * 开始播放
     */
    public synchronized void startPlay(Boolean formUser) {
//        if (PlayerManagerHelper.getInstance().invalidPlayAction()) {
//            //当前正在通话中
//            return;
//        }
//        if (PlayerManagerHelper.getInstance().isAudioAdPlayLockOver()) {
//            //如果正在播放广告，直接关闭
//            AdvertisingManager.getInstance().close();
//        }
        //设置标志位
        setFormUser(formUser);

        if (mediaPlayer != null) {
            if (mediaPlayer.isPlaying()) {
                //如果当前正在播放插播，要执行移除，代替播放完成逻辑
                removeDate();
            }
            String url = getPlayDate().getEventDescriptionPath();
            // 服务端配制的eventDescriptionPath音频url首尾里可能会有空格，会导致界面卡死，
            if (!TextUtils.isEmpty(url)){
                url = url.trim();
            }
            if (playIndex == -1) {
                //没有数据
                if (!isPlayEnd) {
                    isPlayEnd = true;
                    notificationPlayState();
                }
                return;
            }
            isPlayEnd = false;
            if (TextUtils.isEmpty(url)) {
                //如果这条的地址为空就删除，播放下一个
                removeDate();
                startPlay();
                return;
            }
            isPlayCrash = true;
            Log.d(TAG, "-----插播播放地址-----" + url);
            try {
                mediaPlayer.reset();
                mediaPlayer.setDataSource(url);//指定路径
                mediaPlayer.prepareAsync();
                mUrl = url;
                needTermUrl = "";
                Log.e(TAG, "===needTermUrl reset 03");
            } catch (Exception e) {
                isPlayCrash = false;
                e.printStackTrace();
            }
        }
    }



    /**
     * 播放提示音
     *
     * @param url
     */
    public synchronized void playTips(String url) {
        isPlayEnd = false;
        if (TextUtils.isEmpty(url)) {
            return;
        }
        Log.d(TAG, "-----提示音播放地址-----" + url);
        try {
            mediaPlayer.reset();
            mediaPlayer.setDataSource(url);//指定路径
            mediaPlayer.prepareAsync();
            mUrl = url;
            needTermUrl = "";
            Log.e(TAG, "===needTermUrl reset 02");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 是否正在播放插播
     *
     * @return
     */
    public boolean isPlayCrash() {
        return isPlayCrash;
    }

    /**
     * 是否有延播
     */
    public boolean isPlayDate() {
        return playList.size() > 0;
    }

    /**
     * 是否有立即插播
     *
     * @return
     */
    public boolean isImmediatePlayDate() {
        return immediatelyplayList.size() > 0;
    }

    /**
     * 暂停播放
     */
    public void pausePlay() {
        if (mediaPlayer != null) {
            mediaPlayer.pause();
        }
    }

    /**
     * 停止播放
     */
    private void stopPlay(boolean handleError) {
        if (mediaPlayer != null) {
            if (handleError) {
                mediaPlayer.reset();
            } else {
                mediaPlayer.pause();
            }
        }
        isPlayCrash = false;
        //插播完毕，判断是否有立即插播
        if (immediatelyplayList.size() > 0) {
//            startPlay();
            if (icrashPlay != null) {
                //通知插播连续播放
                icrashPlay.onPlay(getPlayDate());
            }
            Log.d(TAG, "继续播放：");
            return;
        }

        if (mediaPlayer != null) {
            mediaPlayer.stop();
        }
//        if (isPlay()) {
//            playEnd();
//        } else {
        //被强行打断，也就是失去焦点，也要执行播放完成的逻辑
//            removeDate();
//            if (icrashstate != null && icrashstate.size() > 0) {
//                for (Icrashstate s : icrashstate) {
//                    s.onCrashstate(isPlayEnd ? 0 : 1);
//                }
//                removeIcrashstate();
//            }
//        }
        Log.d(TAG, "停止播放：");

        // 增加延迟确保音频完全停止后再释放焦点，避免快速切换导致的音频截断
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            abandonAudioFocus();//释放焦点
        }, 150); // 150ms延迟，确保音频完全停止
    }

    /**
     * 停止播放提示音
     */
    public void stopPlayTips() {
        if (mediaPlayer != null) {
            mediaPlayer.pause();
        }
        if (mediaPlayer != null) {
            isPlayCrash = false;
            mediaPlayer.stop();

        }
        if (icrashstate != null && icrashstate.size() > 0) {
            for (Icrashstate s : icrashstate) {
                s.onCrashstate(isPlayEnd ? 0 : 1);
            }
            removeIcrashstate();
        }

        Log.d(TAG, "-----------------停止播放提示音------------：");

        // 增加延迟确保音频完全停止后再释放焦点，避免快速切换导致的音频截断
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            abandonAudioFocus();//释放焦点
        }, 100); // 150ms延迟，确保音频完全停止
    }

    /**
     * 获取是否正在播放
     *
     * @return
     */
    public boolean isPlay() {
        return mediaPlayer != null && mediaPlayer.isPlaying();
    }

    /**
     * 销毁
     */
    public void release() {
        Log.d(TAG, "播放销毁：");
        if (mediaPlayer != null) {
            isPlayCrash = false;
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;

            // 增加延迟确保音频完全停止后再释放焦点，避免快速切换导致的音频截断
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                abandonAudioFocus();
            }, 150); // 150ms延迟，确保音频完全停止
        }
    }

    @Override
    public void onAudioFocusChange(int focusChange) {
        Log.i(TAG, "message play onAudioFocusChange status: " + focusChange);
        switch (focusChange) {
            case AUDIOFOCUS_GAIN://你已经得到了音频焦点。
            case AudioManager.AUDIOFOCUS_GAIN_TRANSIENT:
                mediaPlayer.start();
                notifyPlayerResumed(); //通知播放恢复
                break;
            case AudioManager.AUDIOFOCUS_LOSS://你已经失去了音频焦点很长时间了。你必须停止所有的音频播放
                playEnd();
                break;
            case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT://你暂时失去了音频焦点
                if (mediaPlayer.isPlaying()){
                    mediaPlayer.pause();
                    notifyPlayerPaused();  //通知播放暂停
                }
                break;
            case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK://你暂时失去了音频焦点，但你可以小声地继续播放音频（低音量）而不是完全扼杀音频。
                if (mediaPlayer.isPlaying())
                    mediaPlayer.setVolume(0.2f, 0.2f);
                break;
        }
    }

    private void notifyPlayerPaused() {
        Log.d(TAG, "notifyPlayerPaused: 开始通知UI层播放暂停");
        if (icrashstate != null && !icrashstate.isEmpty()) {
            for (Icrashstate listener : icrashstate) {
                listener.onPlayerPaused();
            }
        }
    }

    private void notifyPlayerResumed() {
        Log.d(TAG, "notifyPlayerResumed: 开始通知UI层播放恢复");
        if (icrashstate != null && !icrashstate.isEmpty()) {
            for (Icrashstate listener : icrashstate) {
                listener.onPlayerResumed();
            }
        }
    }

    public void terminatePlayMsg(String msgId) {
        Log.e(TAG, "===needTermUrl reset 01");
        needTermUrl = "";
        if (crashMessageBaseBean == null){
            return;
        }
        if (msgId.equals(crashMessageBaseBean.getMsgId())) {
            crashMessageBaseBean.setTerminatePlayMsg(true);
        }
    }

    public void terminatePlayMsgByUrl(String url) {
        needTermUrl = url;
    }

    public boolean isFormUser() {
        return formUser;
    }

    public void setFormUser(boolean formUser) {
        this.formUser = formUser;
    }
}
