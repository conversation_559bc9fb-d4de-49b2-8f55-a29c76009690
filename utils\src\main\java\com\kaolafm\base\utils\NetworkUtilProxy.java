package com.kaolafm.base.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class NetworkUtilProxy {

    private static final String TAG = "NetworkUtil";
    private static boolean isNetWorkUsable = true;

    private static long lastCheckTime = 0;

    private static final long CHECK_INTERVAL = 1000; // 检查间隔至少1秒

    /**
     * 检查设备是否已连接到网络（不保证可访问互联网）
     *
     * @param context 上下文对象
     * @return 如果设备已连接到网络返回true，否则返回false
     */
    public static boolean isNetworkConnected(Context context) {
        if (context == null) {
            Log.w(TAG, "isNetworkConnected: context is null");
            return false;
        }

        ConnectivityManager connectivityManager =
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

        if (connectivityManager == null) {
            Log.w(TAG, "isNetworkConnected: ConnectivityManager service is null");
            return false;
        }

        Network network = connectivityManager.getActiveNetwork();
        if (network == null) {
            Log.d(TAG, "isNetworkConnected: No active network");
            return false;
        }

        NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(network);
        if (capabilities == null) {
            Log.d(TAG, "isNetworkConnected: No network capabilities");
            return false;
        }

        boolean hasInternet = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
        boolean isValidated = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED);

        Log.d(TAG, "isNetworkConnected: NET_CAPABILITY_INTERNET=" + hasInternet + ", NET_CAPABILITY_VALIDATED=" + isValidated);
        return (isValidated || hasInternet);
    }

    private static final Executor executor = Executors.newSingleThreadExecutor();
    private static Handler sHandler = new Handler(Looper.getMainLooper());

    public interface NetworkCheckCallback {
        void onResult(boolean isAvailable);
    }

    public static void checkInternetAvailability(NetworkCheckCallback callback) {

        // 检查是否在间隔时间内
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCheckTime < CHECK_INTERVAL) {
            // 直接返回上次结果
            if (sHandler == null) {
                sHandler = new Handler(Looper.getMainLooper());
            }
            sHandler.post(() -> {
                callback.onResult(isNetWorkUsable);
            });
            return;
        }

        executor.execute(() -> {
            Log.i(TAG, "checkInternetAvailability");
            boolean isAvailable = false;
            try (Socket socket = new Socket()) {

                // 可以考虑使用多个检测点，提高准确性
                InetSocketAddress[] checkPoints = {
                        new InetSocketAddress("*************", 443),
                        new InetSocketAddress("*******", 53),
                        new InetSocketAddress("*********", 53)
                };

                for (InetSocketAddress address : checkPoints) {
                    try {
                        socket.connect(address, 1500);
                        isAvailable = true;
                        break;
                    } catch (IOException e) {
                        // 尝试下一个地址
                        Log.i(TAG, "IOException:connect ip:" + address);
                    }
                }
            } catch (IOException e) {
                isAvailable = false;
                Log.i(TAG, "Network check error", e);
            }
            Log.i(TAG, "checkInternetAvailability:" + isAvailable);
            lastCheckTime = System.currentTimeMillis();
            boolean finalIsAvailable = isAvailable;
            isNetWorkUsable = isAvailable;
            if (sHandler == null) {
                sHandler = new Handler(Looper.getMainLooper());
            }
            sHandler.post(() -> {
                callback.onResult(finalIsAvailable);
            });
        });
    }

    /**
     * Returns whether the network is available
     */
    public static boolean isNetworkAvailableDefault(Context context) {
        Log.i(TAG, "isNetworkAvailableDefault");

        boolean isConnected = isNetworkConnected(context);
        Log.i(TAG, "isNetworkAvailableDefault isConnected:" + isConnected);
        if (!isConnected) {
            return false;
        }

        checkInternetAvailability(new NetworkCheckCallback() {
            @Override
            public void onResult(boolean isAvailable) {
                isNetWorkUsable = isAvailable;
            }
        });
        return isNetWorkUsable;
    }
}
